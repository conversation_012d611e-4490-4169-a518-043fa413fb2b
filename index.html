
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>YTA Studio Aug2</title>
  

    <style>
      body {
        font-family: 'Roboto', sans-serif; /* YouTube uses Roboto */
        background-color: white;
        color: rgb(23 23 23); /* neutral-900 equivalent */
        transition: background-color 0.2s ease, color 0.2s ease;
      }
      html.dark body {
        background-color: rgb(10 10 10); /* neutral-950 equivalent */
        color: rgb(245 245 245); /* neutral-100 equivalent */
      }

      /* Modern scrollbar styling */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background-color: rgb(245 245 245); /* neutral-100 equivalent */
      }
      html.dark ::-webkit-scrollbar-track {
        background-color: rgba(38 38 38, 0.5); /* neutral-800/50 equivalent */
      }
      ::-webkit-scrollbar-thumb {
        background-color: rgb(163 163 163); /* neutral-400 equivalent */
        border-radius: 6px;
      }
      html.dark ::-webkit-scrollbar-thumb {
        background-color: rgb(82 82 82); /* neutral-600 equivalent */
      }
      ::-webkit-scrollbar-thumb:hover {
        background-color: rgb(115 115 115); /* neutral-500 equivalent */
      }

      /* Utility class to hide scrollbars */
      .no-scrollbar::-webkit-scrollbar { display: none; }
      .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }

      /* Global Animations */
      @keyframes fade-in-fast {
        from { opacity: 0; transform: translateY(-5px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .animate-fade-in-fast { animation: fade-in-fast 0.15s ease-out forwards; }

      @keyframes fade-in-down-menu {
        from { opacity: 0; transform: translateY(-8px) scale(0.98); }
        to { opacity: 1; transform: translateY(0) scale(1); }
      }
      .animate-fade-in-down-menu { animation: fade-in-down-menu 0.15s ease-out forwards; }
      
      @keyframes fade-in-down-notif {
        from { opacity: 0; transform: translateY(-8px) scale(0.98); }
        to { opacity: 1; transform: translateY(0) scale(1); }
      }
      .animate-fade-in-down-notif { animation: fade-in-down-notif 0.15s ease-out forwards; }

      @keyframes slide-in-right-mini {
        from { transform: translateX(100%) scale(0.95); opacity: 0; }
        to { transform: translateX(0) scale(1); opacity: 1; }
      }
      .animate-slide-in-right-mini { animation: slide-in-right-mini 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards; }

    </style>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "@heroicons/react/": "https://esm.sh/@heroicons/react@^2.2.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
  <body>
    <div id="root"></div>
    <script type="module" src="./index.tsx"></script> {/* Ensured leading ./ */}
  </body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
