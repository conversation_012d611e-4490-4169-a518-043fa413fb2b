# YouTube Studio Clone - Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# YouTube Data API v3 Key
# Get your API key from: https://console.developers.google.com/
# Enable YouTube Data API v3 for your project
VITE_YOUTUBE_API_KEY=your_youtube_api_key_here

# Google Gemini AI API Key
# Get your API key from: https://makersuite.google.com/app/apikey
# Used for AI-powered content suggestions and analysis
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Name
VITE_APP_NAME="YouTube Studio Clone"

# Application Version
VITE_APP_VERSION="2.0.0"

# Application Environment
VITE_APP_ENV=development

# Base URL for API calls
VITE_API_BASE_URL=https://www.googleapis.com/youtube/v3

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable AI features
VITE_ENABLE_AI_FEATURES=true

# Enable/disable analytics
VITE_ENABLE_ANALYTICS=true

# Enable/disable live streaming features
VITE_ENABLE_LIVE_STREAMING=true

# Enable/disable performance monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Enable/disable mock data (for development)
VITE_USE_MOCK_DATA=false

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# API cache duration in milliseconds (15 minutes)
VITE_API_CACHE_DURATION=900000

# Maximum number of videos to load per page
VITE_MAX_VIDEOS_PER_PAGE=50

# Virtual list item height in pixels
VITE_VIRTUAL_LIST_ITEM_HEIGHT=200

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development server port
VITE_DEV_PORT=5173

# Enable/disable hot module replacement
VITE_HMR=true

# Enable/disable source maps in development
VITE_SOURCE_MAPS=true

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================

# Google Analytics Measurement ID (optional)
# VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Sentry DSN for error tracking (optional)
# VITE_SENTRY_DSN=https://your-sentry-dsn

# =============================================================================
# SOCIAL MEDIA CONFIGURATION
# =============================================================================

# Social media sharing URLs
VITE_TWITTER_SHARE_URL=https://twitter.com/intent/tweet
VITE_FACEBOOK_SHARE_URL=https://www.facebook.com/sharer/sharer.php
VITE_LINKEDIN_SHARE_URL=https://www.linkedin.com/sharing/share-offsite/

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Content Security Policy (CSP) settings
VITE_CSP_ENABLED=true

# CORS allowed origins (comma-separated)
VITE_CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Local storage key prefix
VITE_STORAGE_PREFIX=yt_studio_

# Session storage enabled
VITE_SESSION_STORAGE_ENABLED=true

# =============================================================================
# UI CONFIGURATION
# =============================================================================

# Default theme (light, dark, system)
VITE_DEFAULT_THEME=system

# Enable/disable animations
VITE_ENABLE_ANIMATIONS=true

# Default language
VITE_DEFAULT_LANGUAGE=en

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Enable/disable test mode
VITE_TEST_MODE=false

# Test API delay in milliseconds
VITE_TEST_API_DELAY=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (error, warn, info, debug)
VITE_LOG_LEVEL=info

# Enable/disable console logging
VITE_CONSOLE_LOGGING=true

# Enable/disable file logging
VITE_FILE_LOGGING=false

# =============================================================================
# NOTES
# =============================================================================

# 1. All VITE_ prefixed variables are exposed to the client-side code
# 2. Never commit actual API keys to version control
# 3. Use different API keys for development and production
# 4. Some features may require additional API keys or services
# 5. Check the documentation for the latest configuration options