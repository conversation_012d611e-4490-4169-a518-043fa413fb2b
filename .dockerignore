# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist
build
out
.next
.nuxt
.vuepress/dist
.serverless

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Vite
.vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Testing
test-results/
playwright-report/
blob-report/
playwright/.cache/
junit.xml

# Storybook
storybook-static

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
CHANGELOG*
CONTRIBUTING*
CODE_OF_CONDUCT*
SECURITY*
LICENSE*
docs/

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Development tools
.editorconfig
.nvmrc
Makefile

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Vercel
.vercel

# Lighthouse reports
lighthouse-reports/

# MSW
public/mockServiceWorker.js

# Auto-generated files
src/types/api.generated.ts
src/types/schema.generated.ts

# Backup files
*.bak
*.backup
*.old

# Database
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt
*.cert

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Generated documentation
docs/api/
docs/build/

# Performance monitoring
.performance/
performance-results/

# Bundle analysis
bundle-analysis/
webpack-bundle-analyzer-report.html

# Sentry
.sentryclirc

# Husky
.husky/

# Local configuration
.env.local
.env.*.local

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript
tsconfig.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Stylelint
.stylelintcache

# Jest
jest.config.*

# Vitest
vitest.config.*

# Playwright
playwright.config.*

# Tailwind
tailwind.config.*

# PostCSS
postcss.config.*

# Vite
vite.config.*

# Rollup
rollup.config.*

# Webpack
webpack.config.*

# Babel
babel.config.*
.babelrc*

# TypeScript
tsconfig*.json

# Linting
.eslintrc*
.prettierrc*
.stylelintrc*

# Editor config
.editorconfig

# Node version
.nvmrc

# Yarn
.yarnrc*
.yarn/

# pnpm
.pnpmfile.cjs
.pnpm-store/

# Rush
rush.json
.rush/

# Nx
nx.json
.nx/

# Turborepo
turbo.json
.turbo/