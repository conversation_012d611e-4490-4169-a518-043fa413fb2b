{"name": "youtube-studio-clone", "version": "2.0.0", "description": "A modern YouTube Studio clone built with React, TypeScript, and Vite", "type": "module", "scripts": {"dev": "vite", "dev:client": "vite", "dev:api": "node server/dev-server.js", "dev:full": "concurrently \"npm run dev:api\" \"npm run dev:client\"", "dev:client-only": "vite", "build": "tsc && vite build", "build:analyze": "ANALYZE=true npm run build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui --config ./vitest.config.ts", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "husky install"}, "dependencies": {"@google/genai": "^1.3.0", "@google/generative-ai": "^0.2.1", "@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.17.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-window": "^1.8.8", "tailwind-merge": "^2.6.0", "zustand": "^4.4.7"}, "devDependencies": {"@axe-core/react": "^4.8.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.17.57", "concurrently": "^8.2.2", "cors": "^2.8.5", "express": "^4.18.2", "@types/react": "^18.3.23", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.7", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.2.0", "msw": "^2.8.7", "postcss": "^8.4.32", "prettier": "^3.1.1", "rimraf": "^5.0.5", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.3.6", "terser": "^5.40.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["youtube", "studio", "clone", "react", "typescript", "vite", "tailwindcss", "zustand", "react-query"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/youtube-studio-clone.git"}, "bugs": {"url": "https://github.com/yourusername/youtube-studio-clone/issues"}, "homepage": "https://github.com/yourusername/youtube-studio-clone#readme"}