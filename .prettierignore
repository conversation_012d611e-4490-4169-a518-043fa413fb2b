# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/
.parcel-cache/
.vite/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Generated files
*.generated.*
*.auto.*

# Documentation
docs/build/

# Test results
test-results/
junit.xml

# Playwright
playwright-report/
test-results/

# MSW
public/mockServiceWorker.js

# Auto-generated files
src/types/api.generated.ts
src/types/schema.generated.ts

# Lock files (optional - uncomment if you want to format them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Config files that should maintain their formatting
*.config.js
*.config.ts
*.config.mjs
*.config.cjs

# Markdown files with special formatting
CHANGELOG.md
LICENSE.md

# Binary files
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.svg
*.woff
*.woff2
*.ttf
*.eot
*.otf
*.mp4
*.webm
*.wav
*.mp3
*.m4a
*.aac
*.oga
*.pdf

# Minified files
*.min.js
*.min.css

# Vendor files
vendor/
third-party/

# Generated API documentation
api-docs/

# Backup files
*.backup
*.bak
*.orig

# Database files
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt
*.cert

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Temporary files
.tmp/
.temp/