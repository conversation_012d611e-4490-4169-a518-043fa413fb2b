import React from 'react';

/**
 * YourDataPage component for displaying and managing user data and privacy settings
 */
const YourDataPage: React.FC = () => {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Your Data in YouTube</h1>
      <p className="text-gray-600 mb-4">View and manage your YouTube activity and data</p>
      <div className="p-4 bg-blue-50 rounded-lg border border-blue-200 text-blue-700">
        <p>This is a placeholder for the user data page that will allow you to view and manage your YouTube activity and privacy settings.</p>
        <p className="mt-2">For demonstration of video components, please visit the <strong>Video Demo</strong> page using the user menu dropdown.</p>
      </div>
    </div>
  );
};

export default YourDataPage;
