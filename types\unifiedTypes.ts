// Unified Type System for YouTube Studio Clone

// Base Entity Types
export interface BaseEntity {
  id: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// User and Authentication Types
export interface User extends BaseEntity {
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  verified: boolean;
  subscriberCount: number;
  totalViews: number;
  joinedDate: Date | string;
  channelBanner?: string;
  socialLinks?: SocialLink[];
  preferences: UserPreferences;
}

export interface SocialLink {
  platform: 'twitter' | 'instagram' | 'facebook' | 'website' | 'other';
  url: string;
  label?: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  playback: PlaybackSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  comments: boolean;
  likes: boolean;
  subscribers: boolean;
  uploads: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  showSubscriptions: boolean;
  showPlaylists: boolean;
  allowComments: boolean;
  allowMessages: boolean;
}

export interface PlaybackSettings {
  autoplay: boolean;
  quality: 'auto' | '144p' | '240p' | '360p' | '480p' | '720p' | '1080p' | '1440p' | '2160p';
  speed: number;
  captions: boolean;
  volume: number;
}

// Video Types
export interface Video extends BaseEntity {
  title: string;
  description: string;
  thumbnail: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  comments: number;
  shares: number;
  url: string;
  embedUrl?: string;
  channelId: string;
  channelName: string;
  channelAvatar?: string;
  tags: string[];
  category: VideoCategory;
  visibility: VideoVisibility;
  monetization: MonetizationSettings;
  analytics: VideoAnalytics;
  processing: ProcessingStatus;
  quality: VideoQuality[];
  captions: Caption[];
  chapters: Chapter[];
  endScreen?: EndScreen;
  cards: Card[];
}

export interface VideoMetadata {
  title: string;
  description: string;
  tags: string[];
  category: VideoCategory;
  thumbnail?: File | string;
  visibility: VideoVisibility;
  publishDate?: Date;
  language: string;
  location?: string;
  license: 'standard' | 'creative_commons';
}

export interface VideoQuality {
  resolution: string;
  url: string;
  fileSize: number;
  bitrate: number;
  codec: string;
}

export interface Caption {
  id: string;
  language: string;
  label: string;
  url: string;
  isDefault: boolean;
  isAutoGenerated: boolean;
}

export interface Chapter {
  id: string;
  title: string;
  startTime: number;
  thumbnail?: string;
}

export interface EndScreen {
  duration: number;
  elements: EndScreenElement[];
}

export interface EndScreenElement {
  type: 'video' | 'playlist' | 'subscribe' | 'channel';
  position: { x: number; y: number; width: number; height: number };
  targetId?: string;
  customMessage?: string;
}

export interface Card {
  id: string;
  type: 'video' | 'playlist' | 'channel' | 'link' | 'poll';
  title: string;
  message?: string;
  startTime: number;
  endTime?: number;
  targetId?: string;
  url?: string;
  pollOptions?: string[];
}

export type VideoCategory = 
  | 'Entertainment'
  | 'Education'
  | 'Gaming'
  | 'Music'
  | 'News'
  | 'Sports'
  | 'Technology'
  | 'Travel'
  | 'Lifestyle'
  | 'Comedy'
  | 'Science'
  | 'Other';

export type VideoVisibility = 'public' | 'unlisted' | 'private' | 'scheduled';

export interface MonetizationSettings {
  enabled: boolean;
  adTypes: AdType[];
  sponsorships: boolean;
  merchandise: boolean;
  superChat: boolean;
  channelMemberships: boolean;
}

export type AdType = 'pre_roll' | 'mid_roll' | 'post_roll' | 'overlay' | 'skippable' | 'non_skippable';

export interface ProcessingStatus {
  status: 'uploading' | 'processing' | 'ready' | 'failed';
  progress: number;
  message?: string;
  estimatedTime?: number;
}

// Analytics Types
export interface VideoAnalytics {
  views: AnalyticsMetric;
  watchTime: AnalyticsMetric;
  engagement: EngagementMetrics;
  audience: AudienceMetrics;
  traffic: TrafficMetrics;
  revenue?: RevenueMetrics;
}

export interface AnalyticsMetric {
  total: number;
  change: number;
  changePercentage: number;
  data: DataPoint[];
}

export interface DataPoint {
  date: string;
  value: number;
}

export interface EngagementMetrics {
  likes: AnalyticsMetric;
  dislikes: AnalyticsMetric;
  comments: AnalyticsMetric;
  shares: AnalyticsMetric;
  subscribersGained: AnalyticsMetric;
  clickThroughRate: AnalyticsMetric;
  averageViewDuration: AnalyticsMetric;
}

export interface AudienceMetrics {
  demographics: DemographicData;
  geography: GeographicData;
  devices: DeviceData;
  playbackLocations: PlaybackLocationData;
}

export interface DemographicData {
  ageGroups: { range: string; percentage: number }[];
  gender: { male: number; female: number; other: number };
}

export interface GeographicData {
  countries: { country: string; percentage: number; views: number }[];
  cities: { city: string; country: string; percentage: number }[];
}

export interface DeviceData {
  types: { device: string; percentage: number }[];
  operatingSystems: { os: string; percentage: number }[];
}

export interface PlaybackLocationData {
  youtube: number;
  embedded: number;
  mobile: number;
  other: number;
}

export interface TrafficMetrics {
  sources: TrafficSource[];
  searchTerms: SearchTerm[];
  externalSites: ExternalSite[];
}

export interface TrafficSource {
  source: string;
  views: number;
  percentage: number;
}

export interface SearchTerm {
  term: string;
  views: number;
  impressions: number;
  clickThroughRate: number;
}

export interface ExternalSite {
  site: string;
  views: number;
  percentage: number;
}

export interface RevenueMetrics {
  totalRevenue: AnalyticsMetric;
  adRevenue: AnalyticsMetric;
  membershipRevenue: AnalyticsMetric;
  superChatRevenue: AnalyticsMetric;
  merchandiseRevenue: AnalyticsMetric;
  rpm: AnalyticsMetric; // Revenue per mille
  cpm: AnalyticsMetric; // Cost per mille
}

// Playlist Types
export interface Playlist extends BaseEntity {
  title: string;
  description: string;
  thumbnail: string;
  visibility: VideoVisibility;
  videos: PlaylistVideo[];
  totalDuration: number;
  totalViews: number;
  channelId: string;
  channelName: string;
}

export interface PlaylistVideo {
  videoId: string;
  position: number;
  addedAt: Date | string;
}

// Comment Types
export interface Comment extends BaseEntity {
  content: string;
  author: CommentAuthor;
  likes: number;
  dislikes: number;
  replies: Comment[];
  isEdited: boolean;
  isPinned: boolean;
  isHearted: boolean;
  videoId: string;
  parentId?: string;
}

export interface CommentAuthor {
  id: string;
  name: string;
  avatar: string;
  isChannelOwner: boolean;
  isVerified: boolean;
}

// Subscription Types
export interface Subscription extends BaseEntity {
  channelId: string;
  channelName: string;
  channelAvatar: string;
  notificationLevel: 'all' | 'personalized' | 'none';
  subscriberSince: Date | string;
}

// Notification Types
export interface Notification extends BaseEntity {
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  actionUrl?: string;
  thumbnail?: string;
  metadata?: Record<string, any>;
}

export type NotificationType = 
  | 'new_video'
  | 'live_stream'
  | 'comment'
  | 'like'
  | 'subscribe'
  | 'mention'
  | 'system'
  | 'milestone';

// Upload Types
export interface UploadProgress {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
  speed: number;
  timeRemaining: number;
  error?: string;
}

export interface UploadSettings {
  quality: 'original' | 'high' | 'medium' | 'low';
  privacy: VideoVisibility;
  notifications: boolean;
  processing: 'fast' | 'quality';
}

// Search Types
export interface SearchResult {
  type: 'video' | 'channel' | 'playlist';
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  channelName?: string;
  views?: number;
  duration?: number;
  publishedAt?: Date | string;
  relevanceScore: number;
}

export interface SearchFilters {
  type?: 'video' | 'channel' | 'playlist';
  duration?: 'short' | 'medium' | 'long';
  uploadDate?: 'hour' | 'today' | 'week' | 'month' | 'year';
  sortBy?: 'relevance' | 'date' | 'views' | 'rating';
  quality?: 'hd' | '4k';
  features?: ('live' | 'hd' | '4k' | 'subtitles' | 'creative_commons')[];
}

// API Types
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date';
  required: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: ValidationRule[];
  defaultValue?: any;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// UI Component Types
export interface DropdownOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  group?: string;
}

export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  current?: boolean;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Theme Types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  borderRadius: string;
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// Context Types
export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
  refreshToken: () => Promise<void>;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  displayName: string;
  acceptTerms: boolean;
}

export interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

export interface MiniplayerContextType {
  isVisible: boolean;
  video: Video | null;
  showMiniplayer: (video: Video) => void;
  hideMiniplayer: () => void;
  clearMiniplayer: () => void;
}

// Hook Types
export interface UseApiOptions {
  immediate?: boolean;
  refreshInterval?: number;
  retryCount?: number;
  retryDelay?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  mutate: (newData: T) => void;
}

export interface UseFormOptions {
  initialValues?: Record<string, any>;
  validationSchema?: Record<string, ValidationRule[]>;
  onSubmit?: (values: Record<string, any>) => Promise<void> | void;
}

export interface UseFormReturn {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
  setValue: (name: string, value: any) => void;
  setError: (name: string, error: string) => void;
  setTouched: (name: string, touched: boolean) => void;
  handleSubmit: (e: React.FormEvent) => void;
  reset: () => void;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type Nullable<T> = T | null;

export type ValueOf<T> = T[keyof T];

export type ArrayElement<T> = T extends (infer U)[] ? U : never;

export type PromiseType<T> = T extends Promise<infer U> ? U : never;

// Event Types
export interface CustomEvent<T = any> {
  type: string;
  data: T;
  timestamp: number;
}

export interface VideoEvent extends CustomEvent {
  type: 'play' | 'pause' | 'seek' | 'ended' | 'timeupdate' | 'volumechange' | 'fullscreen';
  data: {
    videoId: string;
    currentTime: number;
    duration: number;
    volume?: number;
    isFullscreen?: boolean;
  };
}

export interface AnalyticsEvent extends CustomEvent {
  type: 'page_view' | 'video_view' | 'click' | 'search' | 'engagement';
  data: {
    page?: string;
    videoId?: string;
    element?: string;
    query?: string;
    action?: string;
    value?: number;
  };
}

// All types are already exported individually above