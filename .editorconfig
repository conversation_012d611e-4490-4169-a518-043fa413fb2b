# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, TSX
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# CSS, SCSS, LESS
[*.{css,scss,less}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# Package.json
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[*.{config,conf}]
indent_style = space
indent_size = 2

# Docker files
[{Dockerfile,Dockerfile.*}]
indent_style = space
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2
end_of_line = lf

# Python files
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# Makefile
[{Makefile,makefile,*.mk}]
indent_style = tab
indent_size = 4

# Go files
[*.go]
indent_style = tab
indent_size = 4

# Rust files
[*.rs]
indent_style = space
indent_size = 4

# C/C++ files
[*.{c,cpp,h,hpp}]
indent_style = space
indent_size = 4

# Java files
[*.java]
indent_style = space
indent_size = 4

# PHP files
[*.php]
indent_style = space
indent_size = 4

# Ruby files
[*.rb]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# Environment files
[.env*]
indent_style = space
indent_size = 2

# Git files
[.git*]
indent_style = space
indent_size = 2

# License files
[LICENSE*]
indent_style = space
indent_size = 2

# README files
[README*]
trim_trailing_whitespace = false

# Changelog files
[CHANGELOG*]
trim_trailing_whitespace = false