import React, { useRef, useEffect } from 'react';
import { useVideoPlayer } from '../hooks';
import { Video, VideoQuality, Subtitle, Chapter } from '../types';

interface RefactoredVideoPlayerProps {
  video: Video;
  qualities?: VideoQuality[];
  subtitles?: Subtitle[];
  chapters?: Chapter[];
  autoplay?: boolean;
  muted?: boolean;
  onTimeUpdate?: (currentTime: number) => void;
  onEnded?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

/**
 * Refactored Video Player demonstrating the use of the useVideoPlayer hook
 * 
 * This component shows how the custom hook reduces code duplication:
 * - useVideoPlayer manages all video state (playing, muted, currentTime, etc.)
 * - Centralized video event handlers
 * - Simplified component logic focused on UI rendering
 * - Consistent video player behavior across the app
 * 
 * Compare this with the original AdvancedVideoPlayer to see the reduction in complexity
 */
const RefactoredVideoPlayer: React.FC<RefactoredVideoPlayerProps> = ({
  video,
  qualities = [],
  subtitles = [],
  chapters = [],
  autoplay = false,
  muted = false,
  onTimeUpdate,
  onEnded,
  onError,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // Use the custom video player hook
  const {
    // State
    isPlaying,
    isMuted,
    volume,
    currentTime,
    duration,
    isFullscreen,
    isLoading,
    error,
    playbackRate,
    quality,
    
    // Actions
    togglePlayPause,
    toggleMute,
    setVolume,
    seekTo,
    toggleFullscreen,
    setPlaybackRate,
    setQuality,
    
    // Event handlers
    handleLoadStart,
    handleLoadedData,
    handleTimeUpdate,
    handleEnded,
    handleError,
    handleVolumeChange
  } = useVideoPlayer({
    videoRef,
    autoplay,
    muted,
    onTimeUpdate,
    onEnded,
    onError
  });

  // Format time for display
  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Get current chapter
  const getCurrentChapter = (): Chapter | undefined => {
    return chapters.find((chapter, index) => {
      const nextChapter = chapters[index + 1];
      return currentTime >= chapter.startTime && 
             (!nextChapter || currentTime < nextChapter.startTime);
    });
  };

  const currentChapter = getCurrentChapter();

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      {/* Video Element - Disabled to prevent loading errors */}
      {false && (
        <video
          ref={videoRef}
          src={video.url}
          poster={video.thumbnail}
          className="w-full h-full object-contain"
          onLoadStart={handleLoadStart}
          onLoadedData={handleLoadedData}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleEnded}
          onError={handleError}
          onVolumeChange={handleVolumeChange}
          playsInline
        >
          {/* Subtitles */}
          {subtitles.map((subtitle) => (
            <track
              key={subtitle.language}
              kind="subtitles"
            src={subtitle.url}
            srcLang={subtitle.language}
            label={subtitle.label}
          />
        ))}
        </video>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}

      {/* Error Overlay */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
          <div className="text-center text-white p-6">
            <div className="text-xl mb-2">⚠️</div>
            <div className="text-sm">{error}</div>
            <button
              onClick={() => videoRef.current?.load()}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Controls Overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
        {/* Current Chapter */}
        {currentChapter && (
          <div className="text-white text-sm mb-2 opacity-75">
            {currentChapter.title}
          </div>
        )}

        {/* Progress Bar */}
        <div className="mb-4">
          <input
            type="range"
            min={0}
            max={duration}
            value={currentTime}
            onChange={(e) => seekTo(Number(e.target.value))}
            className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
          />
          
          {/* Chapter Markers */}
          {chapters.length > 0 && (
            <div className="relative mt-1">
              {chapters.map((chapter) => (
                <div
                  key={chapter.startTime}
                  className="absolute w-1 h-2 bg-yellow-400 rounded"
                  style={{ left: `${(chapter.startTime / duration) * 100}%` }}
                  title={chapter.title}
                />
              ))}
            </div>
          )}
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-4">
            {/* Play/Pause */}
            <button
              onClick={togglePlayPause}
              className="hover:text-blue-400 transition-colors"
              aria-label={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>

            {/* Volume */}
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleMute}
                className="hover:text-blue-400 transition-colors"
                aria-label={isMuted ? 'Unmute' : 'Mute'}
              >
                {isMuted ? (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.846 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.846l3.537-3.816a1 1 0 011.617.816zM16.707 9.293a1 1 0 010 1.414L15.414 12l1.293 1.293a1 1 0 01-1.414 1.414L14 13.414l-1.293 1.293a1 1 0 01-1.414-1.414L12.586 12l-1.293-1.293a1 1 0 011.414-1.414L14 10.586l1.293-1.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.846 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.846l3.537-3.816a1 1 0 011.617.816zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
              
              <input
                type="range"
                min={0}
                max={1}
                step={0.1}
                value={volume}
                onChange={(e) => setVolume(Number(e.target.value))}
                className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Time Display */}
            <div className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Playback Speed */}
            <select
              value={playbackRate}
              onChange={(e) => setPlaybackRate(Number(e.target.value))}
              className="bg-transparent text-white text-sm border border-gray-600 rounded px-2 py-1"
            >
              <option value={0.5}>0.5x</option>
              <option value={0.75}>0.75x</option>
              <option value={1}>1x</option>
              <option value={1.25}>1.25x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
            </select>

            {/* Quality */}
            {qualities.length > 0 && (
              <select
                value={quality}
                onChange={(e) => setQuality(e.target.value)}
                className="bg-transparent text-white text-sm border border-gray-600 rounded px-2 py-1"
              >
                {qualities.map((q) => (
                  <option key={q.label} value={q.label}>
                    {q.label}
                  </option>
                ))}
              </select>
            )}

            {/* Fullscreen */}
            <button
              onClick={toggleFullscreen}
              className="hover:text-blue-400 transition-colors"
              aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RefactoredVideoPlayer;

return (
  <div className={`relative ${className}`}>
    {/* Disabled video element */}
    {false && (
      <video
        ref={videoRef}
        src={video.videoUrl}
        className="w-full h-full object-cover"
        muted={isMuted}
        playsInline
      />
    )}
    
    {/* Static thumbnail */}
    <img
      src={video.thumbnailUrl}
      alt={video.title}
      className="w-full h-full object-cover"
    />

    {/* Player controls overlay */}
    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
      <div className="flex items-center gap-4">
        <button 
          onClick={() => {}} // No-op
          className="text-white hover:text-gray-200 transition-colors"
        >
          {isPlaying ? <PauseIcon /> : <PlayIcon />}
        </button>
        
        {/* Time display */}
        <span className="text-white text-sm">
          {formatTime(currentTime)} / {formatTime(duration)}
        </span>
      </div>
    </div>
  </div>
);