import { Video } from '../types';

type PlayerState = {
  isPlaying: boolean;
  isMuted: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  playbackRate: number;
  isFullscreen: boolean;
  isTheaterMode: boolean;
  isMiniPlayer: boolean;
  isPip: boolean;
  showControls: boolean;
  buffered: TimeRanges | null;
  error: MediaError | null;
};

export const initialPlayerState: PlayerState = {
  isPlaying: false,
  isMuted: false,
  volume: 1,
  currentTime: 0,
  duration: 0,
  playbackRate: 1,
  isFullscreen: false,
  isTheaterMode: false,
  isMiniPlayer: false,
  isPip: false,
  showControls: true,
  buffered: null,
  error: null,
};

export function formatTime(seconds: number): string {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = Math.floor(seconds % 60);
  
  if (h > 0) {
    return `${h}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  }
  return `${m}:${s.toString().padStart(2, '0')}`;
}

export function getVideoQualityOptions(video: Video) {
  // In a real app, this would come from the video's available streams
  return [
    { label: 'Auto', value: 'auto' },
    { label: '144p', value: 'tiny' },
    { label: '240p', value: 'small' },
    { label: '360p', value: 'medium' },
    { label: '480p', value: 'large' },
    { label: '720p', value: 'hd720' },
    { label: '1080p', value: 'hd1080' },
    { label: '1440p', value: 'hd1440' },
    { label: '2160p', value: 'hd2160' },
    { label: '4320p', value: 'highres' },
  ].filter(option => {
    // Filter out unsupported qualities based on video metadata
    if (!video.definition) return true;
    
    const qualityOrder = [
      'tiny', 'small', 'medium', 'large', 
      'hd720', 'hd1080', 'hd1440', 'hd2160', 'highres'
    ];
    
    const videoQualityIndex = qualityOrder.indexOf(video.definition);
    const optionIndex = qualityOrder.indexOf(option.value);
    
    return optionIndex <= videoQualityIndex || option.value === 'auto';
  });
}

export function getPlaybackRateOptions() {
  return [
    { label: '0.25x', value: 0.25 },
    { label: '0.5x', value: 0.5 },
    { label: '0.75x', value: 0.75 },
    { label: 'Normal', value: 1 },
    { label: '1.25x', value: 1.25 },
    { label: '1.5x', value: 1.5 },
    { label: '1.75x', value: 1.75 },
    { label: '2x', value: 2 },
  ];
}

export function getVideoChapters(video: Video) {
  // In a real app, this would parse the video's chapter data
  if (!video.duration) return [];
  
  const duration = parseDuration(video.duration);
  if (!duration) return [];
  
  // Generate some sample chapters
  const chapterCount = Math.min(Math.floor(duration / 60), 10); // Max 10 chapters
  if (chapterCount <= 1) return [];
  
  const chapterDuration = duration / chapterCount;
  return Array.from({ length: chapterCount }, (_, i) => ({
    start: Math.floor(i * chapterDuration),
    end: Math.min(Math.floor((i + 1) * chapterDuration), duration),
    title: `Chapter ${i + 1}`,
  }));
}

function parseDuration(duration: string): number | null {
  // Simple duration parser - in a real app, use a proper parser
  const parts = duration.split(':');
  if (parts.length === 3) {
    const [h, m, s] = parts.map(Number);
    return h * 3600 + m * 60 + s;
  }
  if (parts.length === 2) {
    const [m, s] = parts.map(Number);
    return m * 60 + s;
  }
  return null;
}

export function getVideoCaptions(video: Video) {
  // In a real app, this would fetch available captions
  return [
    { id: 'en', label: 'English', language: 'en', isAutoGenerated: false },
    { id: 'es', label: 'Spanish', language: 'es', isAutoGenerated: false },
    { id: 'fr', label: 'French', language: 'fr', isAutoGenerated: false },
    { id: 'de', label: 'German', language: 'de', isAutoGenerated: false },
    { id: 'ja', label: 'Japanese', language: 'ja', isAutoGenerated: false },
    { id: 'en-auto', label: 'English (auto-generated)', language: 'en', isAutoGenerated: true },
  ];
}

export function getVideoEndScreenItems(video: Video) {
  // In a real app, this would fetch recommended videos
  const count = 4;
  return Array.from({ length: count }, (_, i) => ({
    id: `endscreen-${i}`,
    title: `Recommended Video ${i + 1}`,
    thumbnailUrl: video.thumbnailUrl,
    duration: '10:30',
    viewCount: '1.2M',
    channelName: 'Channel Name',
    isLive: false,
  }));
}
